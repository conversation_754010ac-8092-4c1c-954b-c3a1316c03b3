import React, { memo, useState, useRef, ReactNode } from 'react'
import { cn } from '@/components/lib/utils'
import { TreeNode } from '@/components/TreeList'
import { createPortal } from 'react-dom'
import { Ellipsis, Folder, FolderInput } from 'lucide-react'
import { FolderAction } from '@/pages/Projects/material/components/MediaItem'
import { PasterResource } from '@/types/resources.ts'

interface LocalDirItemProps {
  folder?: TreeNode
  resource?: PasterResource.PasterLocal
  currentFolderId: string
  /**
   * 切换目录回调
   */
  onFolderChange: (folderId: string) => void
  /**
   * 本地文件夹操作列表
   */
  LocalActions: FolderAction[]
  /**
   * 是否是本地资源
   */
  isResource?: boolean
  index?: number
  renderResourceItem?: (resource: PasterResource.PasterLocal, index: number) => ReactNode
}

/**
 * 本地文件夹
 */
export const LocalDirItem: React.FC<LocalDirItemProps> = memo(
  ({ folder, currentFolderId, onFolderChange, LocalActions, resource, isResource, index, renderResourceItem }) => {
    const [popupDir, setPopupDir] = useState(false)
    const ellipsisRef = useRef<HTMLSpanElement>(null)
    return (
      <div
        className={cn(
          'dark:hover:bg-blue-200 dark:hover:text-black text-white p-1 rounded-lg text-xs',
          popupDir ? 'dark:bg-blue-200 dark:text-black' : '',
        )}
      >
        <div
          className={cn(
            'relative group aspect-square rounded-lg flex cursor-pointer transition-colors',
            !isResource
              ? 'bg-blue-100 dark:bg-blue-800 hover:bg-blue-200 dark:hover:bg-blue-700'
              : 'dark:bg-gray-800/40',
          )}
          onClick={() => {
            if (!isResource && folder) onFolderChange(folder.id)
          }}
        >
          <div className="w-full h-full">
            {isResource && resource ? (
              renderResourceItem ? (
                renderResourceItem(resource, index!)
              ) : (
                <div> </div>
              )
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Folder className="w-[30%] h-[30%]" />
              </div>
            )}
          </div>

          {folder && (
            <div className="absolute bottom-1 left-1 px-1 py-[2px] text-black rounded bg-blue-200 group-hover:opacity-0 transition-opacity">
              {folder.raw?.resourcesCount}个素材
            </div>
          )}

          {/* 悬浮显示操作图标 */}
          <div
            className={cn(
              'absolute bottom-1 inset-x-1 rounded py-1 flex text-black items-center justify-evenly bg-blue-200 transition-opacity',
              popupDir ? 'opacity-100' : 'opacity-0 group-hover:opacity-100',
            )}
          >
            <FolderInput className="w-4 h-4">
              {' '}
              <title>添加到轨道</title>
            </FolderInput>
            <div className="w-px h-4 bg-black" />

            <span
              className=""
              ref={ellipsisRef}
              onClick={e => {
                e.stopPropagation()
              }}
              onMouseEnter={() => setPopupDir(true)}
              onMouseLeave={() => setPopupDir(false)}
            >
              <Ellipsis className="w-4 h-4" />
            </span>

            {popupDir &&
              createPortal(
                (() => {
                  const rect = ellipsisRef.current?.getBoundingClientRect()
                  if (!rect) return null
                  return (
                    <div
                      style={{
                        position: 'absolute',
                        top: rect.bottom + window.scrollY,
                        left: rect.left + window.scrollX,
                        zIndex: 9999,
                      }}
                      className="p-1 top-0 right-0"
                      onMouseEnter={() => setPopupDir(true)}
                      onMouseLeave={() => setPopupDir(false)}
                    >
                      <div className="border dark:bg-neutral-800 rounded shadow-lg w-[150px] py-2 px-4">
                        {LocalActions.map((action, idx) => (
                          <div
                            key={idx}
                            className="flex items-center px-3 py-1 cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-700"
                            onClick={e => {
                              e.stopPropagation()
                              if (isResource && resource) {
                                action.onClick?.(resource.fileId, currentFolderId, resource.title)
                              } else if (!isResource && folder) {
                                action.onClick?.(folder.id, currentFolderId, folder.label)
                              }
                            }}
                          >
                            {action.icon}
                            <span className="ml-2">{action.label}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )
                })(),
                document.body,
              )}
          </div>
        </div>
        {/* 文件名字 */}
        <div className="flex items-center justify-center py-1">
          <span className="text-sm truncate">{isResource ? (resource?.title ?? '') : (folder?.label ?? '')}</span>
        </div>
      </div>
    )
  },
)

LocalDirItem.displayName = 'LocalDirItem'

export default LocalDirItem
