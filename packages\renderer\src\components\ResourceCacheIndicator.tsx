import React, { useMemo } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { Loader2Icon, PlusIcon } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { useResourceCacheIndicator } from '@/hooks/useResourceCacheIndicator'

export interface ResourceCacheIndicatorProps {
  /**
   * 资源类型
   */
  resourceType: ResourceType
  /**
   * 资源URL
   */
  resourceUrl: string

  /**
   * 是否正在加载
   */
  isLoading?: boolean
  /**
   * 图标大小
   */
  size?: number
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 点击下载按钮的回调
   */
  onDownload?: () => void
  version?: string

}

/**
 * 资源缓存状态指示器
 * 显示资源是否已缓存到本地
 */
export function ResourceCacheIndicator({
  resourceType,
  resourceUrl,
  isLoading: externalLoading,
  size = 16,
  className,
  onDownload,
  version
}: ResourceCacheIndicatorProps) {
  // 使用专门的 hook 来管理缓存指示器状态
  const {
    isCached,
    isLoading: resourceIsLoading,
    checking,
    downloadError,
    downloadResource,
    clearError
  } = useResourceCacheIndicator(resourceType, resourceUrl)

  // 合并外部加载状态和资源加载状态
  const isLoading = useMemo(() => {
    return Boolean(externalLoading || resourceIsLoading)
  }, [externalLoading, resourceIsLoading])

  const handleDownload = async (e: React.MouseEvent) => {
    e.stopPropagation()

    if (onDownload) {
      onDownload()
    } else {
      try {
        // 清除之前的错误状态
        clearError()

        // 使用新的下载函数
        await downloadResource(version)
      } catch (error) {
        // 错误已经在 hook 中处理，这里只需要记录
        console.error('下载资源失败:', error)
      }
    }
  }

  return (
    <div
      className={cn(
        'flex items-center justify-center bg-gray-900/80 rounded p-1 cursor-pointer text-gray-500 hover:bg-gray-900/50 transition-all',
        // 添加错误状态的样式
        downloadError && 'border border-red-500/50',
        className
      )}
      // 添加错误提示
      title={downloadError || (isCached ? '资源已缓存' : '点击下载资源')}
    >
      {checking ? (
        <Loader2Icon
          className="animate-spin text-gray-400 cursor-pointer"
          style={{ width: size, height: size }}
        />
      ) : isLoading ? (
        <Loader2Icon
          className="animate-spin text-blue-500 cursor-pointer"
          style={{ width: size, height: size }}
        />
      ) : downloadError ? (
        <button
          onClick={handleDownload}
          className="group"
        >
          <PlusIcon
            className="text-red-400 hover:text-red-300 cursor-pointer group-hover:animate-pulse"
            style={{ width: size, height: size }}
          />
        </button>
      ) : isCached ? (
        <button
          onClick={handleDownload}
        >
          <PlusIcon
            className="hover:text-green-400 text-green-500 cursor-pointer"
            style={{ width: size, height: size }}
          />
        </button>
      ) : (
        <button
          onClick={handleDownload}
        >
          <PlusIcon
            className="text-gray-400 hover:text-blue-500 cursor-pointer"
            style={{ width: size, height: size }}
          />
        </button>
      )}
    </div>
  )
}
