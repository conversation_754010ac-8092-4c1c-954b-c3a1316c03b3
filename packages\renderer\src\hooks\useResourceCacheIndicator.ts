import { useCallback, useEffect, useState } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { useQueryResourceCacheStatus, useQueryResourceLoading } from '@/hooks/queries/useQueryResourceCacheStatus'
import { useResource } from '@rve/editor/hooks/resource/useResource.tsx'
import { queryClient } from '@/main'
import { QUERY_KEYS } from '@/constants/queryKeys'

/**
 * 专门为 ResourceCacheIndicator 组件设计的 hook
 * 提供更好的状态管理和实时更新机制
 */
export function useResourceCacheIndicator(resourceType: ResourceType, resourceUrl: string) {
  const { downloadResourceToCache, setResourceLoadingState } = useResource()
  
  // 本地状态用于立即响应用户操作
  const [localIsLoading, setLocalIsLoading] = useState(false)
  const [downloadError, setDownloadError] = useState<string | null>(null)

  const { 
    data: isCached, 
    isLoading: checking, 
    refetch: refetchCacheStatus,
    error: cacheError 
  } = useQueryResourceCacheStatus(resourceType, resourceUrl)

  const { 
    data: isResourceLoading, 
    refetch: refetchLoadingStatus,
    error: loadingError 
  } = useQueryResourceLoading(resourceType, resourceUrl)

  // 合并本地加载状态和远程加载状态
  const isLoading = localIsLoading || isResourceLoading || false

  // 强制刷新所有相关查询
  const forceRefreshQueries = useCallback(async () => {
    try {
      // 清除所有相关的 react-query 缓存
      await queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.RESOURCE_CACHE_STATUS, resourceType, resourceUrl]
      })
      await queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.RESOURCE_LOADING, resourceType, resourceUrl]
      })
      await queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.RESOURCE_CACHE]
      })
      
      // 手动触发重新获取
      await Promise.all([
        refetchCacheStatus(),
        refetchLoadingStatus()
      ])
    } catch (error) {
      console.error('刷新查询失败:', error)
    }
  }, [resourceType, resourceUrl, refetchCacheStatus, refetchLoadingStatus])

  // 下载资源的函数
  const downloadResource = useCallback(async (version?: string) => {
    setDownloadError(null)
    setLocalIsLoading(true)

    try {
      // 更新远程加载状态
      await setResourceLoadingState(resourceType, resourceUrl, true)
      
      // 立即刷新加载状态查询以反映UI变化
      await refetchLoadingStatus()

      // 执行下载
      const result = await downloadResourceToCache({
        url: resourceUrl,
        resourceType,
        version: version || '1.0.0',
        customExt: resourceType === ResourceType.MUSIC ? 'mp3' : undefined
      })

      if (!result) {
        throw new Error('下载失败：未返回本地路径')
      }

      // 下载成功后等待一小段时间确保状态已更新
      await new Promise(resolve => setTimeout(resolve, 200))
      
      // 刷新所有相关查询
      await forceRefreshQueries()
      
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '下载失败'
      console.error('下载资源失败:', error)
      setDownloadError(errorMessage)
      
      // 确保在出错时也重置加载状态
      await setResourceLoadingState(resourceType, resourceUrl, false)
      await refetchLoadingStatus()
      
      throw error
    } finally {
      setLocalIsLoading(false)
    }
  }, [resourceType, resourceUrl, setResourceLoadingState, downloadResourceToCache, refetchLoadingStatus, forceRefreshQueries])

  // 清除错误状态
  const clearError = useCallback(() => {
    setDownloadError(null)
  }, [])

  // 监听加载状态变化，自动清除本地加载状态
  useEffect(() => {
    if (!isResourceLoading && localIsLoading) {
      // 如果远程加载状态为 false 但本地状态为 true，延迟清除本地状态
      const timer = setTimeout(() => {
        setLocalIsLoading(false)
      }, 500)
      
      return () => clearTimeout(timer)
    }
  }, [isResourceLoading, localIsLoading])

  // 提供重试机制
  const retry = useCallback(async (version?: string) => {
    clearError()
    return downloadResource(version)
  }, [clearError, downloadResource])

  return {
    // 状态
    isCached: isCached || false,
    isLoading,
    checking,
    downloadError,
    
    // 错误状态
    hasError: !!(cacheError || loadingError || downloadError),
    error: cacheError || loadingError || downloadError,
    
    // 操作函数
    downloadResource,
    retry,
    clearError,
    forceRefreshQueries,
    
    // 原始查询函数（用于高级用法）
    refetchCacheStatus,
    refetchLoadingStatus
  }
}
