import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { useResource } from '@rve/editor/hooks/resource/useResource.tsx'
import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { queryClient } from '@/main'

/**
 * 查询资源缓存状态的 hook
 * @param resourceType 资源类型
 * @param resourceUrl 资源URL
 * @returns 缓存状态的查询结果
 */
export const useQueryResourceCacheStatus = (resourceType?: ResourceType, resourceUrl?: string) => {
  const { isResourceCached } = useResource()

  return useQuery({
    queryKey: [QUERY_KEYS.RESOURCE_CACHE_STATUS, resourceType, resourceUrl],
    queryFn: async () => {
      if (!resourceType || !resourceUrl) return false

      // 直接查询最新的缓存状态，不依赖 react-query 的缓存
      // 这确保了下载完成后能立即获取到最新状态
      return await isResourceCached(resourceType, resourceUrl)
    },
    // 缓存状态相对稳定，可以设置较长的 staleTime
    staleTime: 30000, // 30秒内不重新获取
    // 不设置自动刷新间隔，依赖手动触发
    refetchInterval: false,
    // 组件挂载时获取最新状态
    refetchOnMount: true,
    // 窗口获得焦点时不自动刷新，避免不必要的 IPC 调用
    refetchOnWindowFocus: false,
    enabled: !!resourceType && !!resourceUrl,
  })
}

/**
 * 查询资源加载状态的 hook
 * @param resourceType 资源类型
 * @param resourceUrl 资源URL
 * @returns 加载状态的查询结果
 */
export const useQueryResourceLoading = (resourceType?: ResourceType, resourceUrl?: string) => {
  const { isResourceLoading } = useResource()

  return useQuery({
    queryKey: [QUERY_KEYS.RESOURCE_LOADING, resourceType, resourceUrl],
    queryFn: async () => {
      if (!resourceType || !resourceUrl) return false
      return await isResourceLoading(resourceType, resourceUrl)
    },
    // 不缓存加载状态，始终获取最新状态
    staleTime: 0,
    // 短间隔轮询以获取实时加载状态
    refetchInterval: (data) => {
      // 如果正在加载，则每500ms检查一次；否则停止轮询
      return data ? 500 : false
    },
    // 组件挂载时获取最新状态
    refetchOnMount: true,
    // 窗口获得焦点时刷新状态
    refetchOnWindowFocus: true,
    enabled: !!resourceType && !!resourceUrl,
  })
}
